"""
Test script for the functional PR generator implementation.

This script tests the refactored functional programming approach.
"""

import sys
import os

# Add the backend directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from services.pr_generator import initialize_pr_generator, generate_pr_statement
    print("✅ Successfully imported functional PR generator")
except ImportError as e:
    print(f"❌ Failed to import functional PR generator: {e}")
    sys.exit(1)


def test_initialization():
    """Test the initialization of the functional PR generator."""
    try:
        initialize_pr_generator()
        print("✅ PR generator initialized successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to initialize PR generator: {e}")
        return False


def test_pr_generation():
    """Test PR statement generation using functional approach."""
    try:
        test_topic = "Revolutionary AI-powered customer service platform"
        pr_statement = generate_pr_statement(test_topic)
        
        if pr_statement and len(pr_statement.strip()) > 0:
            print("✅ PR statement generated successfully")
            print(f"   Topic: {test_topic}")
            print(f"   Generated PR Statement: {pr_statement[:100]}...")
            return True
        else:
            print("❌ Generated PR statement is empty")
            return False
    except Exception as e:
        print(f"❌ Failed to generate PR statement: {e}")
        return False


def main():
    """Run functional tests."""
    print("🧪 Testing Functional PR Generator Implementation")
    print("=" * 55)
    
    # Test initialization
    print("1. Testing initialization...")
    init_success = test_initialization()
    
    if not init_success:
        print("\n❌ Initialization failed. Cannot proceed with generation test.")
        print("Make sure:")
        print("   - .env file exists with GROQ_API_KEY")
        print("   - All dependencies are installed")
        return False
    
    # Test PR generation
    print("\n2. Testing PR statement generation...")
    generation_success = test_pr_generation()
    
    print("\n" + "=" * 55)
    print("📊 Test Results:")
    print(f"   Initialization: {'✅' if init_success else '❌'}")
    print(f"   PR Generation: {'✅' if generation_success else '❌'}")
    
    if init_success and generation_success:
        print("\n🎉 All functional tests passed!")
        print("The refactored functional implementation is working correctly.")
        return True
    else:
        print("\n⚠️  Some tests failed.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
